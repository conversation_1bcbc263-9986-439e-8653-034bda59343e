import React from "react";
import { View, Text, Image, StyleSheet, Dimensions, TouchableOpacity } from "react-native";
import { Colors } from "../constants/colors";
import { MapPinIcon } from "phosphor-react-native";
import { JobType } from "../types";
import { useNavigation } from "@react-navigation/native";

const CARD_WIDTH = 144;
const CARD_HEIGHT = 220;

interface JobCardProps {
  job: JobType;
}

export default function JobCard({ job }: JobCardProps) {
    const navigation = useNavigation<any>(); // <any> pour éviter les erreurs TS pour l'instant

    const { image, title, dates, localisation, price, tag } = job;

    return (
        <TouchableOpacity style={styles.card} 
            onPress={() => navigation.navigate("ExploreDetails", { job })}
        >
            <View style={styles.imageContainer}>

                <Image source={{ uri: image }} style={styles.image} resizeMode="cover" />
                {tag && (
                    <View style={styles.badge}>
                        <Text style={styles.badgeText}>{tag}</Text>
                    </View>
                )}
                <View style={styles.price}>
                    <Text style={styles.priceText}>€{price}</Text>
                </View>
            </View>
            <View style={styles.content}>
                <Text style={styles.title}>{title}</Text>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <MapPinIcon size={12} color={Colors.black} />
                    <Text style={styles.localisation}>{localisation}</Text>
                </View>
                <Text style={styles.localisation}>{dates}</Text>
            </View>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    card: {
        width: CARD_WIDTH,
        height: CARD_HEIGHT,
        borderRadius: 16,
        overflow: "hidden",
        marginRight: 15,
        shadowColor: "#000",
        shadowOpacity: 0.1,
        // shadowOffset: { width: 0, height: 2 },
        // shadowRadius: 6,
        // elevation: 5,
    },
    imageContainer: {
        width: "100%",
        height: "70%",
        position: "relative",
    },
    image: {
        width: "100%",
        height: "100%",  // environ la moitié de la carte pour l'image
        borderRadius: 16,
    },

    badge: {
        position: "absolute",
        top: 10,
        left: 10,
        backgroundColor: Colors.grey,
        paddingVertical: 2,
        paddingHorizontal: 8,
        borderRadius: 12,
    },
    badgeText: {
        color: Colors.black,
        fontSize: 12,
        fontFamily: "Pais-bold",
    },
    price: {
        position: "absolute",
        bottom: 0,
        right: 0,
        backgroundColor: "#fff",
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderBottomEndRadius: 16,
        borderTopStartRadius: 16,
        
        shadowOffset: { width: 0, height: -2 },
        shadowRadius: 6,
        elevation: 5,
        shadowColor: "#000",
        
    },
    priceText: {
        color: Colors.black,
        fontSize: 18,
        fontFamily: "Pais-bold",
    },



    content: {
        flex: 1,
        padding: 2,
        paddingTop: 6,
        // justifyContent: "space-between",
    },
    title: {
        fontSize: 12,
        lineHeight: 14,
        // fontWeight: "bold",
        fontFamily: "Pais-bold",
    },
    localisation: {
        fontSize: 10,
        fontFamily: "Pais",
        color: "#555",
    },

});
