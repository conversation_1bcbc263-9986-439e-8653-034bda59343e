import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import ExploreDetailsScreen from "../../screens/App/Explore/ExploreDetailsScreen";
import ExploreTabs from "../../screens/App/Explore/ExploreTabs";

const Stack = createNativeStackNavigator();

export default function ExploreStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="ExploreMain"
        component={ExploreTabs}
        options={{ headerShown: false, title: "Explorer" }}
      />
    </Stack.Navigator>
  );
}
