import React, { createContext, useContext, useEffect, useState } from "react";
import { supabase } from "../lib/Supabase";

const AuthContext = createContext<any>(null);

export const AuthProvider: React.FC<React.PropsWithChildren<{}>> = ({ children }) => {
    const [user, setUser] = useState<import('@supabase/supabase-js').User | null>(null);
    const [loading, setLoading] = useState(true);

    // Vérifie si un user est déjà connecté au démarrage
    useEffect(() => {
        const session = supabase.auth.getSession().then(({ data: { session } }) => {
            setUser(session?.user ?? null);
            setLoading(false);
        });

        // Écoute les changements d'auth
        const { data: subscription } = supabase.auth.onAuthStateChange(
            (_event, session) => {
                setUser(session?.user ?? null);
            }
        );

        return () => subscription.subscription.unsubscribe();
    }, []);

    interface LoginParams {
        email: string;
        password: string;
    }

    const login = async ({ email, password }: LoginParams): Promise<void> => {
        const { error } = await supabase.auth.signInWithPassword({ email, password });
        
        if (error) throw error;
    };

    const signup = async ({ email, password }: LoginParams): Promise<void> => {
        const { error } = await supabase.auth.signUp({ email, password });
        if (error) throw error;
    };

    const logout = async () => {
        await supabase.auth.signOut();
    };

    return (
        <AuthContext.Provider value={{ user, loading, login, signup, logout }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
  return useContext(AuthContext);
};
