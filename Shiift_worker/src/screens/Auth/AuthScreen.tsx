import React, { useState } from "react";
import { View, Text, TextInput, Button, StyleSheet, Alert, Image } from "react-native";
import { useAuth } from "../../context/AuthContext";

export default function AuthScreen() {
  const { login, signup } = useAuth();
  const [email, setEmail] = useState<string>("<EMAIL>");
  const [password, setPassword] = useState<string>("mvp");

  const handleLogin = async () => {
    try {
      await login({email, password});
    } catch (error: any) {
      Alert.alert("Erreur", error.message);
    }
  };

  const handleSignup = async () => {
    try {
      await signup(email, password);
      Alert.alert("Succès", "Vérifie ton email pour confirmer ton compte !");
    } catch (error: any) {
      Alert.alert("Erreur", error.message);
    }
  };

  return (
    <View style={styles.container}>
        <View style={{ alignItems: "center", justifyContent: "center" }}>
            <Image source={require("../../../assets/icon.png")} />
        </View>
        <View>

            <Text style={styles.title}>Connexion</Text>

            <TextInput
                style={styles.input}
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
            />

            <TextInput
                style={styles.input}
                placeholder="Mot de passe"
                secureTextEntry
                value={password}
                onChangeText={setPassword}
            />

            <Button title="Se connecter" onPress={handleLogin} />
            <View style={{ height: 10 }} />
            <Button title="Créer un compte" onPress={handleSignup} />
        </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    padding: 20,
    backgroundColor: "#fff",

  },
  title: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "Pais-bold",
    marginBottom: 20,
    textAlign: "center",
  },
  input: {
    borderWidth: 1,
    fontFamily: "Pais",
    borderColor: "#ccc",
    padding: 10,
    marginBottom: 15,
    borderRadius: 8,
  },
});
