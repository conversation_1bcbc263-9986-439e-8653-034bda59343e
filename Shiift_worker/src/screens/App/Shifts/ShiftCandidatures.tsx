import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  StatusBar,
  TouchableOpacity,
  Image
} from "react-native";
import {
  HourglassHighIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyEurIcon
} from "phosphor-react-native";
import { Colors } from "../../../constants/colors";
import { JobType } from "../../../types";

interface Application {
  id: string;
  job: JobType;
  status: 'pending' | 'accepted' | 'rejected';
  appliedDate: string;
  responseDate?: string;
}

// Données fictives pour les candidatures
const applications: Application[] = [
  {
    id: "1",
    job: {
      id: "1",
      title: "Restaurant le Chookat",
      dates: "Jeudi 28 Août - 8h - 15h",
      localisation: "Fontainebleau",
      price: 110,
      image: "https://dynamic-media-cdn.tripadvisor.com/media/photo-o/15/15/7e/c8/le-bacchus.jpg",
      tag: "Serveur"
    },
    status: 'pending',
    appliedDate: "20 Septembre 2024"
  },
  {
    id: "2",
    job: {
      id: "2",
      title: "Le 5 étoiles",
      dates: "Vendredi 29 Août - 18h - 23h",
      localisation: "Fontainebleau",
      price: 150,
      image: "https://latelier-du-gout-fontainebleau.com/wp-content/uploads/2024/11/photos-restaurant-latelier-du-gout-fontainebleau-2.jpg",
      tag: "Barman"
    },
    status: 'accepted',
    appliedDate: "18 Septembre 2024",
    responseDate: "19 Septembre 2024"
  },
  {
    id: "3",
    job: {
      id: "3",
      title: "Café Central",
      dates: "Samedi 30 Août - 10h - 14h",
      localisation: "Fontainebleau",
      price: 80,
      image: "https://picsum.photos/200",
      tag: "Serveur"
    },
    status: 'rejected',
    appliedDate: "15 Septembre 2024",
    responseDate: "17 Septembre 2024"
  },
  {
    id: "4",
    job: {
      id: "4",
      title: "Brasserie du Parc",
      dates: "Dimanche 31 Août - 12h - 18h",
      localisation: "Fontainebleau",
      price: 95,
      image: "https://picsum.photos/201",
      tag: "Serveur"
    },
    status: 'pending',
    appliedDate: "22 Septembre 2024"
  },
  {
    id: "5",
    job: {
      id: "5",
      title: "Le Vintage",
      dates: "Lundi 1er Sept - 19h - 2h",
      localisation: "Fontainebleau",
      price: 125,
      image: "https://picsum.photos/202",
      tag: "Barman"
    },
    status: 'pending',
    appliedDate: "21 Septembre 2024"
  }
];

export default function ShiftCandidatures() {
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'accepted' | 'rejected'>('all');

  // Filtrer les candidatures selon le filtre sélectionné
  const filteredApplications = selectedFilter === 'all'
    ? applications
    : applications.filter(app => app.status === selectedFilter);

  // Calculer les statistiques
  const pendingCount = applications.filter(app => app.status === 'pending').length;
  const acceptedCount = applications.filter(app => app.status === 'accepted').length;
  const rejectedCount = applications.filter(app => app.status === 'rejected').length;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
        return <CheckCircleIcon size={20} color={Colors.success} weight="fill" />;
      case 'rejected':
        return <XCircleIcon size={20} color={Colors.error} weight="fill" />;
      case 'pending':
        return <ClockIcon size={20} color={Colors.warning} weight="fill" />;
      default:
        return <HourglassHighIcon size={20} color={Colors.textSecondary} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'Acceptée';
      case 'rejected':
        return 'Refusée';
      case 'pending':
        return 'En attente';
      default:
        return 'En cours';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return Colors.success;
      case 'rejected':
        return Colors.error;
      case 'pending':
        return Colors.warning;
      default:
        return Colors.textSecondary;
    }
  };

  const renderApplicationCard = ({ item }: { item: Application }) => (
    <TouchableOpacity style={styles.applicationCard}>
      <View style={styles.cardHeader}>
        <Image source={{ uri: item.job.image }} style={styles.jobImage} />
        <View style={styles.jobInfo}>
          <Text style={styles.jobTitle}>{item.job.title}</Text>
          <Text style={styles.jobTag}>{item.job.tag}</Text>
          <View style={styles.jobDetails}>
            <View style={styles.detailRow}>
              <MapPinIcon size={14} color={Colors.textSecondary} />
              <Text style={styles.detailText}>{item.job.localisation}</Text>
            </View>
            <View style={styles.detailRow}>
              <CalendarIcon size={14} color={Colors.textSecondary} />
              <Text style={styles.detailText}>{item.job.dates}</Text>
            </View>
            <View style={styles.detailRow}>
              <CurrencyEurIcon size={14} color={Colors.success} />
              <Text style={[styles.detailText, { color: Colors.success, fontWeight: 'bold' }]}>
                €{item.job.price}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.applicationInfo}>
        <View style={styles.statusContainer}>
          {getStatusIcon(item.status)}
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {getStatusText(item.status)}
          </Text>
        </View>
        <Text style={styles.appliedDate}>Candidature: {item.appliedDate}</Text>
        {item.responseDate && (
          <Text style={styles.responseDate}>Réponse: {item.responseDate}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />

      {/* Header */}
      <View style={styles.header}>
        <View style={{ flexDirection: "column", alignItems: "flex-start" }}>
          <Text style={styles.headerText}>Mes</Text>
          <Text style={styles.headerText}>Candidatures</Text>
        </View>
        <View style={{ flexDirection: "column", alignItems: "center" }}>
          <HourglassHighIcon size={40} color="#fff" />
          <Text style={styles.headerSecondaryText}>{applications.length} candidatures</Text>
        </View>
      </View>

      <View>
        {/* Carte principale - Résumé */}
        <View style={styles.summaryCard}>
          <Text style={styles.summaryCardText}>
            {pendingCount > 0
              ? `${pendingCount} candidature${pendingCount > 1 ? 's' : ''} en attente de réponse`
              : "Aucune candidature en attente"
            }
          </Text>
        </View>

        {/* Section Statistiques */}
        <View style={styles.scrollSection}>
          <Text style={styles.sectionTitle}>Statistiques</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statsCard}>
              <Text style={styles.statsNumber}>{pendingCount}</Text>
              <Text style={styles.statsLabel}>En attente</Text>
            </View>
            <View style={styles.statsCard}>
              <Text style={styles.statsNumber}>{acceptedCount}</Text>
              <Text style={styles.statsLabel}>Acceptées</Text>
            </View>
            <View style={styles.statsCard}>
              <Text style={styles.statsNumber}>{rejectedCount}</Text>
              <Text style={styles.statsLabel}>Refusées</Text>
            </View>
          </View>
        </View>

        {/* Filtres */}
        <View style={styles.scrollSection}>
          <Text style={styles.sectionTitle}>Filtrer par statut</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ paddingHorizontal: 30 }}>
            <TouchableOpacity
              style={[styles.filterButton, selectedFilter === 'all' && styles.filterButtonActive]}
              onPress={() => setSelectedFilter('all')}
            >
              <Text style={[styles.filterText, selectedFilter === 'all' && styles.filterTextActive]}>
                Toutes ({applications.length})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, selectedFilter === 'pending' && styles.filterButtonActive]}
              onPress={() => setSelectedFilter('pending')}
            >
              <Text style={[styles.filterText, selectedFilter === 'pending' && styles.filterTextActive]}>
                En attente ({pendingCount})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, selectedFilter === 'accepted' && styles.filterButtonActive]}
              onPress={() => setSelectedFilter('accepted')}
            >
              <Text style={[styles.filterText, selectedFilter === 'accepted' && styles.filterTextActive]}>
                Acceptées ({acceptedCount})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, selectedFilter === 'rejected' && styles.filterButtonActive]}
              onPress={() => setSelectedFilter('rejected')}
            >
              <Text style={[styles.filterText, selectedFilter === 'rejected' && styles.filterTextActive]}>
                Refusées ({rejectedCount})
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Liste des candidatures */}
        <View style={styles.scrollSection}>
          <Text style={styles.sectionTitle}>Mes candidatures</Text>
          <FlatList
            data={filteredApplications}
            keyExtractor={(item) => item.id}
            renderItem={renderApplicationCard}
            contentContainerStyle={{ paddingHorizontal: 30 }}
            scrollEnabled={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <HourglassHighIcon size={64} color={Colors.textSecondary} />
                <Text style={styles.emptyText}>Aucune candidature trouvée</Text>
                <Text style={styles.emptySubtext}>
                  {selectedFilter === 'all'
                    ? "Vous n'avez pas encore postulé à des offres"
                    : `Aucune candidature ${getStatusText(selectedFilter).toLowerCase()} trouvée`
                  }
                </Text>
              </View>
            }
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f0f0f0",
    paddingTop: StatusBar.currentHeight,
  },
  header: {
    width: "100%",
    height: 190,
    backgroundColor: "#000",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingTop: 60,
  },
  headerText: {
    color: "#fff",
    fontSize: 30,
    fontFamily: "Pais-bold",
  },
  headerSecondaryText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "300",
  },
  summaryCard: {
    margin: 30,
    backgroundColor: "#fff",
    borderRadius: 12,
    height: 100,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 5,
  },
  summaryCardText: {
    fontSize: 16,
    fontFamily: "Pais",
    textAlign: "center",
  },
  scrollSection: {
    marginTop: 10,
    marginBottom: 20,
  },
  sectionTitle: {
    marginLeft: 30,
    fontSize: 18,
    fontFamily: "Pais-bold",
    marginBottom: 10,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 30,
  },
  statsCard: {
    flex: 1,
    backgroundColor: "#fff",
    marginHorizontal: 5,
    borderRadius: 12,
    height: 100,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 5,
  },
  statsNumber: {
    fontSize: 20,
    fontFamily: "Pais-bold",
    color: "#000",
  },
  statsLabel: {
    fontSize: 12,
    fontFamily: "Pais",
    color: "#555",
    textAlign: "center",
    marginTop: 5,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#fff",
    marginRight: 10,
    borderWidth: 1,
    borderColor: "transparent",
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 2,
    elevation: 2,
  },
  filterButtonActive: {
    backgroundColor: "#000",
    borderColor: "#000",
  },
  filterText: {
    fontSize: 14,
    color: "#555",
    fontWeight: "500",
    fontFamily: "Pais",
  },
  filterTextActive: {
    color: "#fff",
  },
  applicationCard: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: "row",
    marginBottom: 12,
  },
  jobImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  jobInfo: {
    flex: 1,
  },
  jobTitle: {
    fontSize: 16,
    fontWeight: "bold",
    fontFamily: "Pais-bold",
    color: Colors.text,
    marginBottom: 4,
  },
  jobTag: {
    fontSize: 12,
    color: Colors.textSecondary,
    backgroundColor: Colors.grey,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 8,
  },
  jobDetails: {
    gap: 4,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailText: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 6,
    fontFamily: "Pais",
  },
  applicationInfo: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 6,
    fontFamily: "Pais-bold",
  },
  appliedDate: {
    fontSize: 11,
    color: Colors.textSecondary,
    fontFamily: "Pais",
  },
  responseDate: {
    fontSize: 11,
    color: Colors.textSecondary,
    fontFamily: "Pais",
    fontStyle: "italic",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.textSecondary,
    marginTop: 16,
    marginBottom: 8,
    fontFamily: "Pais-bold",
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
    paddingHorizontal: 40,
    fontFamily: "Pais",
  },
});