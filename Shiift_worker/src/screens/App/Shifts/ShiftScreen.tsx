import { ClockCounterClockwiseIcon, FileTextIcon } from "phosphor-react-native";
import React from "react";
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";

interface Shift {
  id: string;
  title: string;
  date: string;
  location: string;
  time: string;
}

// Exemple de données fictives
const nextShift: Shift = {
  id: "1",
  title: "Serveur - Restaurant Le Chookat",
  date: "Jeudi 19 Septembre",
  location: "Fontainebleau",
  time: "8h - 15h",
};

const upcomingShifts: Shift[] = [
  { id: "2", title: "Barman - Le 5 étoiles", date: "Vendredi 20 Septembre", location: "Fontainebleau", time: "18h - 23h" },
  { id: "3", title: "Serveur - Café Central", date: "Samedi 21 Septembre", location: "Fontainebleau", time: "10h - 14h" },
  { id: "4", title: "Plongeur - Le Gourmet", date: "Dimanche 22 Septembre", location: "Fontainebleau", time: "9h - 13h" },
];

export default function ShiftScreen() {
  const navigation = useNavigation<any>();

  return (
    <SafeAreaView style={styles.container}>
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Mes Shifts</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity style={styles.headerButton}
            onPress={() => navigation.navigate('ShiftCandidatures')}
          >
            <FileTextIcon size={32} color="#000" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('ShiftHistorique')}
          >
            <ClockCounterClockwiseIcon size={32} color="#000" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Prochain shift */}
      <Text style={styles.sectionTitle}>Prochain shift</Text>
      <TouchableOpacity style={styles.nextShiftCard}>
        <Text style={styles.shiftTitle}>{nextShift.title}</Text>
        <Text style={styles.shiftInfo}>{nextShift.date} · {nextShift.time}</Text>
        <Text style={styles.shiftInfo}>{nextShift.location}</Text>
      </TouchableOpacity>

      {/* Shifts à venir */}
      <Text style={styles.sectionTitle}>Shifts à venir</Text>
      <FlatList
        data={upcomingShifts}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingBottom: 20 }}
        renderItem={({ item }) => (
          <View style={styles.upcomingCard}>
            <Text style={styles.shiftTitle}>{item.title}</Text>
            <Text style={styles.shiftInfo}>{item.date} · {item.time}</Text>
            <Text style={styles.shiftInfo}>{item.location}</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff", paddingHorizontal: 20 },

  header: { flexDirection: "row", alignItems: "center", justifyContent: "space-between", marginTop: 10 },
  headerTitle: { fontSize: 28, fontWeight: "bold" },
  headerButtons: { flexDirection: "row" },
  headerButton: { marginLeft: 15 },

  sectionTitle: { fontSize: 20, fontWeight: "600", marginTop: 25, marginBottom: 10 },

  nextShiftCard: {
    backgroundColor: "#f5f5f5",
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  shiftTitle: { fontSize: 16, fontWeight: "bold", marginBottom: 6 },
  shiftInfo: { fontSize: 14, color: "#555" },

  upcomingCard: {
    backgroundColor: "#fafafa",
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
});
