import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ScrollView
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import {
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  CurrencyEurIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockCounterClockwiseIcon
} from "phosphor-react-native";
import { Colors } from "../../../constants/colors";

interface HistoricalShift {
  id: string;
  title: string;
  date: string;
  location: string;
  time: string;
  earnings: number;
  status: 'completed' | 'cancelled' | 'no-show';
  company: string;
  duration: string;
}

// Données fictives pour l'historique
const historicalShifts: HistoricalShift[] = [
  {
    id: "1",
    title: "Serveur",
    company: "Restaurant Le Chookat",
    date: "15 Septembre 2024",
    location: "Fontainebleau",
    time: "8h - 15h",
    duration: "7h",
    earnings: 110,
    status: 'completed'
  },
  {
    id: "2",
    title: "Barman",
    company: "Le 5 étoiles",
    date: "12 Septembre 2024",
    location: "Fontainebleau",
    time: "18h - 23h",
    duration: "5h",
    earnings: 85,
    status: 'completed'
  },
  {
    id: "3",
    title: "Serveur",
    company: "Café Central",
    date: "10 Septembre 2024",
    location: "Fontainebleau",
    time: "10h - 14h",
    duration: "4h",
    earnings: 0,
    status: 'cancelled'
  },
  {
    id: "4",
    title: "Plongeur",
    company: "Le Gourmet",
    date: "8 Septembre 2024",
    location: "Fontainebleau",
    time: "9h - 13h",
    duration: "4h",
    earnings: 60,
    status: 'completed'
  },
  {
    id: "5",
    title: "Serveur",
    company: "Brasserie du Parc",
    date: "5 Septembre 2024",
    location: "Fontainebleau",
    time: "12h - 18h",
    duration: "6h",
    earnings: 0,
    status: 'no-show'
  },
  {
    id: "6",
    title: "Barman",
    company: "Le Vintage",
    date: "3 Septembre 2024",
    location: "Fontainebleau",
    time: "19h - 2h",
    duration: "7h",
    earnings: 125,
    status: 'completed'
  }
];

export default function ShiftHistorique() {
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'completed' | 'cancelled' | 'no-show'>('all');

  // Filtrer les shifts selon le filtre sélectionné
  const filteredShifts = selectedFilter === 'all'
    ? historicalShifts
    : historicalShifts.filter(shift => shift.status === selectedFilter);

  // Calculer les statistiques
  const completedShifts = historicalShifts.filter(shift => shift.status === 'completed');
  const totalEarnings = completedShifts.reduce((sum, shift) => sum + shift.earnings, 0);
  const totalHours = completedShifts.reduce((sum, shift) => sum + parseInt(shift.duration), 0);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon size={20} color={Colors.success} weight="fill" />;
      case 'cancelled':
        return <XCircleIcon size={20} color={Colors.warning} weight="fill" />;
      case 'no-show':
        return <XCircleIcon size={20} color={Colors.error} weight="fill" />;
      default:
        return <ClockIcon size={20} color={Colors.textSecondary} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Terminé';
      case 'cancelled':
        return 'Annulé';
      case 'no-show':
        return 'Absent';
      default:
        return 'En cours';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return Colors.success;
      case 'cancelled':
        return Colors.warning;
      case 'no-show':
        return Colors.error;
      default:
        return Colors.textSecondary;
    }
  };

  const renderShiftCard = ({ item }: { item: HistoricalShift }) => (
    <TouchableOpacity style={styles.shiftCard}>
      <View style={styles.cardHeader}>
        <View style={styles.titleContainer}>
          <Text style={styles.shiftTitle}>{item.title}</Text>
          <Text style={styles.companyName}>{item.company}</Text>
        </View>
        <View style={styles.statusContainer}>
          {getStatusIcon(item.status)}
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {getStatusText(item.status)}
          </Text>
        </View>
      </View>

      <View style={styles.cardContent}>
        <View style={styles.infoRow}>
          <CalendarIcon size={16} color={Colors.textSecondary} />
          <Text style={styles.infoText}>{item.date}</Text>
        </View>

        <View style={styles.infoRow}>
          <ClockIcon size={16} color={Colors.textSecondary} />
          <Text style={styles.infoText}>{item.time} ({item.duration})</Text>
        </View>

        <View style={styles.infoRow}>
          <MapPinIcon size={16} color={Colors.textSecondary} />
          <Text style={styles.infoText}>{item.location}</Text>
        </View>

        {item.status === 'completed' && (
          <View style={styles.earningsRow}>
            <CurrencyEurIcon size={16} color={Colors.success} />
            <Text style={styles.earningsText}>€{item.earnings}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>

      {/* Statistiques */}
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.statsContainer}>
          <View style={styles.statsCard}>
            <Text style={styles.statsNumber}>{completedShifts.length}</Text>
            <Text style={styles.statsLabel}>Shifts terminés</Text>
          </View>
          <View style={styles.statsCard}>
            <Text style={styles.statsNumber}>€{totalEarnings}</Text>
            <Text style={styles.statsLabel}>Gains totaux</Text>
          </View>
          <View style={styles.statsCard}>
            <Text style={styles.statsNumber}>{totalHours}h</Text>
            <Text style={styles.statsLabel}>Heures travaillées</Text>
          </View>
        </View>

        {/* Filtres */}
        <View style={styles.filtersContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[styles.filterButton, selectedFilter === 'all' && styles.filterButtonActive]}
              onPress={() => setSelectedFilter('all')}
            >
              <Text style={[styles.filterText, selectedFilter === 'all' && styles.filterTextActive]}>
                Tous ({historicalShifts.length})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, selectedFilter === 'completed' && styles.filterButtonActive]}
              onPress={() => setSelectedFilter('completed')}
            >
              <Text style={[styles.filterText, selectedFilter === 'completed' && styles.filterTextActive]}>
                Terminés ({completedShifts.length})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, selectedFilter === 'cancelled' && styles.filterButtonActive]}
              onPress={() => setSelectedFilter('cancelled')}
            >
              <Text style={[styles.filterText, selectedFilter === 'cancelled' && styles.filterTextActive]}>
                Annulés ({historicalShifts.filter(s => s.status === 'cancelled').length})
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.filterButton, selectedFilter === 'no-show' && styles.filterButtonActive]}
              onPress={() => setSelectedFilter('no-show')}
            >
              <Text style={[styles.filterText, selectedFilter === 'no-show' && styles.filterTextActive]}>
                Absents ({historicalShifts.filter(s => s.status === 'no-show').length})
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Liste des shifts */}
        <FlatList
          data={filteredShifts}
          keyExtractor={(item) => item.id}
          renderItem={renderShiftCard}
          contentContainerStyle={styles.listContainer}
          scrollEnabled={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <ClockCounterClockwiseIcon size={64} color={Colors.textSecondary} />
              <Text style={styles.emptyText}>Aucun shift trouvé</Text>
              <Text style={styles.emptySubtext}>
                {selectedFilter === 'all'
                  ? "Vous n'avez pas encore effectué de shifts"
                  : `Aucun shift ${getStatusText(selectedFilter).toLowerCase()} trouvé`
                }
              </Text>
            </View>
          }
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    padding: 20,
  },

  header: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 10,
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: "bold",
    marginLeft: 12,
    color: Colors.text,
  },

  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 25,
  },
  statsCard: {
    flex: 1,
    backgroundColor: Colors.grey,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  statsNumber: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: "center",
  },

  filtersContainer: {
    marginBottom: 20,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.grey,
    marginRight: 10,
    borderWidth: 1,
    borderColor: "transparent",
  },
  filterButtonActive: {
    backgroundColor: Colors.black,
    borderColor: Colors.black,
  },
  filterText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: "500",
  },
  filterTextActive: {
    color: Colors.white,
  },

  listContainer: {
    paddingBottom: 20,
  },

  shiftCard: {
    backgroundColor: Colors.grey,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
  },
  shiftTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 2,
  },
  companyName: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 4,
  },

  cardContent: {
    gap: 8,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
  earningsRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  earningsText: {
    fontSize: 16,
    fontWeight: "bold",
    color: Colors.success,
    marginLeft: 8,
  },

  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
    paddingHorizontal: 40,
  },
});