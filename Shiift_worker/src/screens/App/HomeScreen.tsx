import React from "react";
import { View, Text, StyleSheet, ScrollView, FlatList, Dimensions, StatusBar, TouchableOpacity } from "react-native";
import { useAuth } from "../../context/AuthContext";
import JobCard from "../../composants/JobCard";
import { ClockClockwiseIcon, ClockCounterClockwiseIcon, FileIcon, HourglassHighIcon, QuestionIcon } from "phosphor-react-native"; // Icônes phosphor
import { Colors } from "../../constants/colors";
import { JobType } from "../../types";
import { useNavigation } from "@react-navigation/native";

const CARD_WIDTH = Dimensions.get("window").width * 0.7;
const CARD_HEIGHT = 120;

export const jobs: JobType[] = [
  {
    id: "1",
    title: "Restaurant le Chookat",
    dates: "Jeudi 28 Août - 8h - 15h",
    localisation: "Fontainebleau",
    price: 110,
    image: "https://dynamic-media-cdn.tripadvisor.com/media/photo-o/15/15/7e/c8/le-bacchus.jpg",
    tag: "Serveur"
  },
  {
    id: "2",
    title: "Le 5 étoiles",
    dates: "Jeudi 28 Août - 8h - 15h",
    localisation: "Fontainebleau",
    price: 150,
    image: "https://latelier-du-gout-fontainebleau.com/wp-content/uploads/2024/11/photos-restaurant-latelier-du-gout-fontainebleau-2.jpg",
    tag: "Serveur"
  },
  {
    id: "3",
    title: "Le 5 étoiles",
    dates: "Jeudi 28 Août - 8h - 15h",
    localisation: "Fontainebleau",
    price: 150,
    image: "https://latelier-du-gout-fontainebleau.com/wp-content/uploads/2024/11/photos-restaurant-latelier-du-gout-fontainebleau-2.jpg",
    tag: "Serveur"
  }
];

// Dummy data accès rapide
const quickAccessItems = [
  { id: "1", title: "Candidatures", link: "ShiftCandidatures", icon: <HourglassHighIcon size={70} color={Colors.black} /> },
  { id: "2", title: "Documents", link: "Documents", icon: <FileIcon size={70} color={Colors.black} /> },
  { id: "3", title: "Historique", link: "ShiftHistorique", icon: <ClockCounterClockwiseIcon size={70} color={Colors.black} /> },
  { id: "4", title: "Help", link: "Help", icon: <QuestionIcon size={70} color={Colors.black} /> },
];

export default function HomeScreen() {
    const navigation = useNavigation<any>(); // <any> pour éviter les erreurs TS pour l'instant
    return (
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
            <StatusBar barStyle="light-content" backgroundColor="#000" />

            {/* Header */}
            <View style={styles.header}>
                <View style={{ flexDirection: "column", alignItems: "left" }}>
                    <Text style={styles.headerText}>Bonjour </Text>
                    <Text style={styles.headerText}>Tymothé </Text>
                </View>
                {/* <Text style={styles.headerText}>Bonjour </Text>
                <Text style={styles.headerText}>Alexandre </Text> */}
                <View style={{ flexDirection: "column", alignItems: "center" }}>
                    <Text style={styles.headerText}>€1200</Text>
                    <Text style={styles.headerSecondaryText}>Les 7 derniers jours</Text>
                </View>
            </View>
            <View style={{ }} >

                {/* Carte principale */}
                <View style={styles.nextShiftCard}>
                    <Text style={styles.nextShiftCardText}>Vous n'avez pas de prochain shift</Text>
                </View>

                {/* Section Autour de vous */}
                <View style={styles.scrollSection}>
                    <Text style={styles.sectionTitle}>Autour de vous</Text>
                    <FlatList
                        data={jobs}
                        horizontal
                        keyExtractor={(item) => item.id}
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ paddingHorizontal: 30 }}
                        renderItem={({ item }) => (
                            <JobCard
                                job={item}
                            />
                        )}
                    />
                </View>

                {/* Section Accès rapide */}
                <View style={styles.scrollSection}>
                    <Text style={styles.sectionTitle}>Accès rapide</Text>
                    <FlatList
                        data={quickAccessItems}
                        horizontal
                        keyExtractor={(item) => item.id}
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ paddingHorizontal: 30 }}
                        renderItem={({ item }) => (
                            <TouchableOpacity style={styles.quickAccessCard}
                                onPress={() => navigation.navigate(item.link)}
                            >
                                {item.icon}
                                <Text style={styles.quickAccessText}>{item.title}</Text>
                            </TouchableOpacity>
                        )}
                    />
                </View>
                {/* Section Statistiques */}
                <View style={styles.scrollSection}>
                    <Text style={styles.sectionTitle}>Statistiques</Text>
                    <View style={styles.statsContainer}>
                        <View style={styles.statsCard}>
                        <Text style={styles.statsNumber}>12</Text>
                        <Text style={styles.statsLabel}>Shifts cette semaine</Text>
                        </View>
                        <View style={styles.statsCard}>
                        <Text style={styles.statsNumber}>€350</Text>
                        <Text style={styles.statsLabel}>Gains cette semaine</Text>
                        </View>
                        <View style={styles.statsCard}>
                        <Text style={styles.statsNumber}>5</Text>
                        <Text style={styles.statsLabel}>Candidatures en cours</Text>
                        </View>
                    </View>
                </View>
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f0f0f0",
    paddingTop: StatusBar.currentHeight,
  },
  header: {
    width: "100%",
    height: 190,
    backgroundColor: "#000",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingTop: 60,
  },
  headerText: {
    color: "#fff",
    fontSize: 30,
    fontFamily: "Pais-bold",

  },
  headerSecondaryText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "300",
  },
  nextShiftCard: {
    margin: 30,
    backgroundColor: "#fff",
    borderRadius: 12,
    height: 100,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 5,
  },
  nextShiftCardText: {
    fontSize: 16,
    fontFamily: "Pais",
  },
  scrollSection: {
    marginTop: 10,
    marginBottom: 20,
  },
  sectionTitle: {
    marginLeft: 30,
    fontSize: 18,
    fontFamily: "Pais-bold",
    marginBottom: 10,
  },
  quickAccessCard: {
    width: 140,
    height: 140,
    backgroundColor: "#fff",
    borderRadius: 12,
    marginRight: 15,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 5,
  },
  quickAccessText: {
    marginTop: 8,
    fontSize: 14,
    fontFamily: "Pais-bold",
    textAlign: "center",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 30,
    },
    statsCard: {
    flex: 1,
    backgroundColor: "#fff",
    marginHorizontal: 5,
    borderRadius: 12,
    height: 100,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 5,
    },
    statsNumber: {
    fontSize: 20,
    fontFamily: "Pais-bold",
    color: "#000",
    },
    statsLabel: {
    fontSize: 12,
    fontFamily: "Pais",
    color: "#555",
    textAlign: "center",
    marginTop: 5,
    },

});
