import React from "react";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import ExploreListScreen from "./ExploreListScreen";
import ExploreMapScreen from "./ExploreMapScreen";
import { SafeAreaView } from "react-native-safe-area-context";
import { Colors } from "../../../constants/colors"; // si tu as déjà défini tes couleurs

const Tab = createMaterialTopTabNavigator();

export default function ExploreTabs() {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "#fff" }} edges={['top']}>
      <Tab.Navigator
        screenOptions={{
          tabBarActiveTintColor: Colors.primary,   // Couleur du texte actif
          tabBarInactiveTintColor: "#888",         // Couleur du texte inactif
          tabBarLabelStyle: { fontSize: 14, fontWeight: "600" }, // Style du texte
          tabBarIndicatorStyle: { 
            backgroundColor: Colors.primary, 
            height: 3, 
            borderRadius: 2 
          }, // Style du soulignement actif
          swipeEnabled: false,
          tabBarStyle: { backgroundColor: "#fff", elevation: 0 }, // Fond des tabs
        }}
      >
        <Tab.Screen name="Liste" component={ExploreListScreen} />
        <Tab.Screen name="Carte" component={ExploreMapScreen} />
      </Tab.Navigator>
    </SafeAreaView>
  );
}
