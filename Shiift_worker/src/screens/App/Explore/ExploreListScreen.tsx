import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, Text, View } from "react-native";
import JobCard from "../../../composants/JobCard";
import { JobType } from "../../../types";

// Section "Les plus proches de vous"
export const nearbyJobs: JobType[] = [
  { id: "1", title: "Serveur - Café du Marché", dates: "Jeudi 28 Août - 8h-12h", localisation: "Paris 3ème", price: 20, image: "https://picsum.photos/200", tag: "Serveur" },
  { id: "2", title: "Barman - Le Rooftop", dates: "Vendredi 29 Août - 18h-23h", localisation: "Paris 10ème", price: 25, image: "https://picsum.photos/201", tag: "Barman" },
  { id: "3", title: "Livreur - Uber Eats", dates: "Samedi 30 Août - 12h-16h", localisation: "Paris 11ème", price: 15, image: "https://picsum.photos/202", tag: "Livreur" },
  { id: "4", title: "Serveur - Brasserie Saint-Germain", dates: "Dimanche 31 Août - 9h-14h", localisation: "Paris 6ème", price: 22, image: "https://picsum.photos/203", tag: "Serveur" },
];

// Section "Les mieux payés"
export const highPaidJobs: JobType[] = [
  { id: "5", title: "Chef de rang - Hôtel Luxe", dates: "Lundi 1er Sept - 18h-23h", localisation: "Paris 8ème", price: 40, image: "https://picsum.photos/204", tag: "Serveur" },
  { id: "6", title: "Barman premium - Club VIP", dates: "Mardi 2 Sept - 20h-2h", localisation: "Paris 1er", price: 45, image: "https://picsum.photos/205", tag: "Barman" },
  { id: "7", title: "Photographe événementiel", dates: "Mercredi 3 Sept - 14h-20h", localisation: "Paris 9ème", price: 50, image: "https://picsum.photos/206", tag: "Photographe" },
  { id: "8", title: "Serveur privé - Dîner entreprise", dates: "Jeudi 4 Sept - 19h-23h", localisation: "Paris 2ème", price: 38, image: "https://picsum.photos/207", tag: "Serveur" },
];

// Section "Dernières annonces"
export const latestJobs: JobType[] = [
  { id: "9", title: "Livreur vélo - Pizza Express", dates: "Aujourd'hui - 18h-22h", localisation: "Paris 12ème", price: 18, image: "https://picsum.photos/208", tag: "Livreur" },
  { id: "10", title: "Serveur - Café de Flore", dates: "Demain - 9h-13h", localisation: "Paris 6ème", price: 20, image: "https://picsum.photos/209", tag: "Serveur" },
  { id: "11", title: "Barman - Bar à cocktails", dates: "Vendredi - 20h-2h", localisation: "Paris 10ème", price: 25, image: "https://picsum.photos/210", tag: "Barman" },
  { id: "12", title: "Assistant événementiel", dates: "Samedi - 10h-17h", localisation: "Paris 15ème", price: 30, image: "https://picsum.photos/211", tag: "Assistant" },
];


export default function ExploreListScreen() {
    const sections = [
        { title: "Les plus proches de vous", data: nearbyJobs },
        { title: "Les mieux payés", data: highPaidJobs },
        { title: "Dernières annonces", data: latestJobs },
    ];
    return (
        <ScrollView>
            {sections.map((section) => (
                <View key={section.title} style={{ marginBottom: 20 }}>
                    <Text style={styles.sectionTitle}>{section.title}</Text>
                    <FlatList
                        data={section.data}
                        keyExtractor={(item) => item.id}
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ paddingHorizontal: 20 }}
                        renderItem={({ item }) => <JobCard job={item} />}
                    />
                </View>
            ))}
        </ScrollView>
    );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  sectionTitle: { fontSize: 18, fontWeight: "bold", marginLeft: 20, marginBottom: 10 },
});
