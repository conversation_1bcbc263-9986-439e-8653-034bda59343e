import React from "react";
import { SafeAreaView, ScrollView, View, Text, Image, TouchableOpacity, StyleSheet } from "react-native";
import { JobType } from "../../../types";
import { NativeStackScreenProps } from "@react-navigation/native-stack";

export default function ExploreDetailsScreen({ route }: { route: { params: { job: JobType } } }) {
    console.log(route.params.job);
  
    const { job } = route.params;

    return (
        <SafeAreaView style={styles.safe}>
            <ScrollView style={styles.container}>
                <Image source={{ uri: job.image }} style={styles.image} />
                <View style={styles.content}>
                <Text style={styles.title}>{job.title}</Text>
                <Text style={styles.desc}>{job.dates}</Text>
                <Text style={styles.desc}>{job.localisation}</Text>
                <Text style={styles.price}>{job.price} € / nuit</Text>
                <TouchableOpacity style={styles.reserveButton}>
                    <Text style={styles.reserveText}>Réserver</Text>
                </TouchableOpacity>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
  safe: { flex: 1, backgroundColor: "#fff" },
  container: { flex: 1 },
  image: { width: "100%", height: 250 },
  content: { padding: 20 },
  title: { fontSize: 22, fontWeight: "bold", marginBottom: 10 },
  desc: { fontSize: 14, color: "#555", marginBottom: 10 },
  price: { fontSize: 18, fontWeight: "bold", color: "#e60023", marginBottom: 20 },
  reserveButton: { backgroundColor: "#e60023", paddingVertical: 12, borderRadius: 8, alignItems: "center" },
  reserveText: { color: "white", fontWeight: "bold", fontSize: 16 },
});
