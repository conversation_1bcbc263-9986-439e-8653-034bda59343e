import React, { useState, useRef } from "react";
import { View, Text, TouchableOpacity, StyleSheet, Animated } from "react-native";
import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE, Region } from "react-native-maps";
import { useNavigation } from "@react-navigation/native";
import { Colors } from "../../../constants/colors";

const places = [
  { id: 1, latitude: 48.8566, longitude: 2.3522, price: 120, title: "Appartement cosy", desc: "2 pers · Paris centre", tag: "Appartement" },
  { id: 2, latitude: 48.8666, longitude: 2.3622, price: 95, title: "Studio moderne", desc: "1 pers · Quartier animé", tag: "Studio" },
  { id: 3, latitude: 48.8526, longitude: 2.3422, price: 150, title: "Loft chic", desc: "3 pers · Quartier branché", tag: "Loft" },
];

export default function ExploreMapScreen() {
  const [selectedPlace, setSelectedPlace] = useState<any>(null);
  const mapRef = useRef<MapView>(null);
  const navigation = useNavigation();

  const centerMapOnPlace = (place: typeof places[0]) => {
    setSelectedPlace(place);
    const region: Region = {
      latitude: place.latitude,
      longitude: place.longitude,
      latitudeDelta: 0.05,
      longitudeDelta: 0.05,
    };
    mapRef.current?.animateToRegion(region, 500);
  };

  return (
    <View style={{ flex: 1 }}>
      <MapView
        ref={mapRef}
        style={{ flex: 1 }}
        provider={PROVIDER_GOOGLE}
        initialRegion={{
          latitude: 48.8566,
          longitude: 2.3522,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        }}
      >
        {places.map((place) => {
          const isSelected = selectedPlace?.id === place.id;
          return (
            <Marker
              key={place.id}
              coordinate={{ latitude: place.latitude, longitude: place.longitude }}
              onPress={() => centerMapOnPlace(place)}
            >
              <Animated.View style={[styles.markerBubble, isSelected && styles.markerBubbleSelected]}>
                <Text style={[styles.markerText, isSelected && styles.markerTextSelected]}>
                  {place.price} €
                </Text>
                <Text style={[styles.markerTag, isSelected && styles.markerTagSelected]}>
                  {place.tag}
                </Text>
              </Animated.View>
            </Marker>
          );
        })}
      </MapView>

      {selectedPlace && (
        <TouchableOpacity
          style={styles.bottomCard}
          onPress={() => navigation.navigate("ExploreDetails", { place: selectedPlace })}
        >
          <Text style={styles.bottomTitle}>{selectedPlace.title}</Text>
          <Text style={styles.bottomDesc}>{selectedPlace.desc}</Text>
          <Text style={styles.bottomPrice}>{selectedPlace.price} € / nuit</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  bottomCard: {
    position: "absolute",
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: "#fff",
    padding: 15,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  bottomTitle: { fontWeight: "bold", fontSize: 16, marginBottom: 2 },
  bottomDesc: { fontSize: 12, color: "#555", marginBottom: 4 },
  bottomPrice: { fontSize: 14, fontWeight: "bold", color: Colors.primary },

  map: { flex: 1 },

  markerBubble: {
    backgroundColor: "#fff",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#ccc",
    shadowColor: "#000",
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 4,
    alignItems: "center",
  },
  markerBubbleSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  markerText: { fontWeight: "bold", color: "#000" },
  markerTextSelected: { color: "#fff" },
  markerTag: { fontSize: 10, color: "#555", marginTop: 2 },
  markerTagSelected: { color: "#fff" },
});
