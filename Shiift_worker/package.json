{"name": "shiift_worker", "license": "0BSD", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@gorhom/bottom-sheet": "^5.2.6", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/material-top-tabs": "^7.3.7", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.26", "@supabase/supabase-js": "^2.57.4", "@tanstack/react-query": "^5.87.4", "expo": "54.0.7", "expo-asset": "^12.0.8", "expo-font": "~14.0.8", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "phosphor-react-native": "^3.0.0", "react": "19.1.0", "react-native": "0.81.4", "react-native-maps": "1.20.1", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1"}, "devDependencies": {"@react-native-community/cli": "latest", "@types/react": "~19.1.0", "@types/react-native": "^0.72.8", "typescript": "~5.9.2"}, "private": true, "rnpm": {"assets": ["./assets/fonts"]}}