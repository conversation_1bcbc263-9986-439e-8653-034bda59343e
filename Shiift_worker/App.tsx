import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "./src/context/AuthContext";
import MainNavigator from "./src/navigation/MainNavigator";
import { useFonts } from "expo-font";
import { ActivityIndicator, Text, View } from "react-native";

const queryClient = new QueryClient();

export default function App() {

    const [fontsLoaded, error] = useFonts({
        Pais: require("./assets/fonts/PaisRegular.otf"),
        "Pais-Bold": require("./assets/fonts/Pais-Bold.otf"),
    });

    if (error) {
        console.error("Erreur de chargement des polices:", error);
    }
    if (!fontsLoaded) {
        return (
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
            <ActivityIndicator size="large" />
        </View>
        );
    }

    // // Appliquer la police à tous les Text
    if (fontsLoaded) {
        (Text as any).defaultProps = {
            ...(Text as any).defaultProps,
            style: { fontFamily: "Pais" },
        };
    }
        
    return (
        <AuthProvider>
            <QueryClientProvider client={queryClient}>
                <MainNavigator />
            </QueryClientProvider>
        </AuthProvider>
    );
}
